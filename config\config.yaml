ai:
  batch_size: 32
  epsilon_decay: 0.995
  epsilon_end: 0.01
  epsilon_start: 1.0
  learning_rate: 0.001
  memory_size: 10000
  model_type: dqn
  target_update_frequency: 100
  training_frequency: 4
backtest:
  commission: 0.02         # Gi<PERSON><PERSON> từ 0.03 xuống 0.02 để phản ánh thực tế
  end_date: '2024-12-31'   # Cập nhật để test với dữ liệu gần đây hơn
  initial_balance: 182     # Giữ nguyên
  spread: 150              # Giảm từ 200 xuống 150 để phù hợp với risk config
  start_date: '2024-01-01' # Cập nhật để test với dữ liệu gần đây hơn
  logging_level: ERROR     # Giữ nguyên để giảm spam log
enable_ai: true
enable_backtesting: false
indicators:
  atr_period: 21           # Tăng từ 14 lên 21 để ổn định hơn cho vàng
  macd_fast: 8             # G<PERSON><PERSON><PERSON> từ 12 xuống 8 để nh<PERSON><PERSON> hơn với thị trường vàng
  macd_signal: 5           # G<PERSON><PERSON><PERSON> từ 9 xuống 5 để phản ứng nhanh hơn
  macd_slow: 21            # Giảm từ 26 xuống 21 để cân bằng
  pivot_method: standard   # Giữ nguyên standard method

market_timing:
  avoid_news_hours: true          # Tránh giao dịch trong giờ tin tức
  news_buffer_minutes: 30         # Tránh 30 phút trước/sau tin tức
  preferred_trading_hours:        # Giờ giao dịch tốt nhất cho vàng
    - start: "08:00"             # London open
      end: "12:00"
    - start: "13:00"             # US session
      end: "17:00"
  avoid_low_volume_hours: true    # Tránh giờ volume thấp
  min_volume_threshold: 1000      # Volume tối thiểu
  session_overlap_bonus: 0.1      # Bonus score khi session overlap
live_trading: true
logging_level: INFO
mt5:
  login: 126780412
  magic_number: 234000
  password: 'Exnessdz123@199'
  server: 'Exness-MT5Real7'
  symbol: XAUUSD
risk:
  max_drawdown: 0.70  # Tăng lên 70% để cho phép backtest chạy đầy đủ
  max_spread: 100     # Giảm xuống 100 để chỉ trade khi spread thấp
  stop_loss_atr_multiplier: 1.5  # Giảm xuống 1.5 để cắt lỗ nhanh hơn
  take_profit_atr_multiplier: 4.5  # Tăng lên 4.5 để có risk-reward 1:3
  trailing_stop: true
  trailing_stop_distance: 15  # Giảm xuống 15 để theo sát hơn
  # Advanced exit strategy parameters
  partial_close_enabled: true     # Bật để giảm rủi ro
  partial_close_at_50_percent: true  # Đóng 50% khi đạt 50% target
  breakeven_enabled: true         # Bật move to breakeven
  breakeven_trigger_ratio: 0.4    # Move to BE sớm hơn khi đạt 40% target
  time_based_exit: true           # Bật thoát lệnh theo thời gian
  max_trade_duration_hours: 6     # Giảm xuống 6 giờ để tránh hold quá lâu
  margin_level_min: 300       # Tăng lên 300 để an toàn hơn
  max_total_risk_exposure: 0.6  # Giảm xuống 60% để an toàn
  max_consecutive_losses: 3   # Giảm xuống 3 lỗ liên tiếp
  daily_loss_limit: 0.05      # Giảm xuống 5% trong ngày
  volatility_filter: true     # Giữ lọc volatility
  min_atr_threshold: 1.0      # Tăng lên 1.0 để chỉ trade khi volatility cao
strategy:
  scoring:
    macd_crossover_bullish: 0.6  # Tăng lên 0.6 để chỉ trade crossover mạnh
    macd_crossover_bearish: 0.6  # Tăng lên 0.6 để chỉ trade crossover mạnh
    macd_trend_bullish: 0.1      # Giảm xuống 0.1 để ít phụ thuộc trend
    macd_trend_bearish: 0.1      # Giảm xuống 0.1 để ít phụ thuộc trend
    pivot_above: 0.35            # Tăng lên 0.35 - pivot rất quan trọng cho vàng
    pivot_below: 0.35            # Tăng lên 0.35 - pivot rất quan trọng cho vàng
    market_trend_bullish: 0.4    # Tăng lên 0.4 - trend tổng thể quan trọng
    market_trend_bearish: 0.4    # Tăng lên 0.4 - trend tổng thể quan trọng
    ai_prediction_weight: 0.2    # Giảm xuống 0.2
    technical_weight: 1.4        # Tăng lên 1.4 để tăng trọng số technical
    ai_weight: 0.0               # Giữ nguyên 0.0 - tắt AI
    min_signal_strength: 0.8     # Tăng lên 0.8 để chỉ trade tín hiệu mạnh hơn
    min_ai_confidence: 0.8       # Tăng lên 0.8

market_filters:
  enable_volatility_filter: true    # Bật lọc volatility
  min_volatility: 1.0              # Tăng ATR tối thiểu để chỉ trade khi volatility rất cao
  max_volatility: 2.2              # Giảm ATR tối đa để tránh thị trường quá biến động
  enable_spread_filter: true       # Bật lọc spread
  max_spread_ratio: 0.0002         # Giảm spread tối đa (0.02%) - chặt hơn
  enable_trend_filter: true        # Bật lọc trend
  min_trend_strength: 0.85         # Tăng độ mạnh trend tối thiểu
  enable_momentum_filter: true     # Bật lọc momentum
  momentum_threshold: 0.75         # Tăng ngưỡng momentum
  avoid_consolidation: true        # Tránh thị trường sideway
  consolidation_threshold: 0.15    # Giảm ngưỡng để tránh consolidation tốt hơn
  # Advanced market condition filters
  enable_volume_filter: true       # Bật lọc volume
  min_volume_ma_ratio: 1.3         # Volume phải > 130% MA - chặt hơn
  enable_time_filter: true         # Bật lọc thời gian
  avoid_market_open_minutes: 45    # Tránh 45 phút đầu mở cửa
  avoid_market_close_minutes: 45   # Tránh 45 phút cuối đóng cửa
  enable_correlation_filter: true  # Bật lọc correlation với USD
  max_usd_correlation: 0.7         # Tránh khi correlation USD cao

trading:
  max_daily_trades: 6      # Giảm xuống 6 để tập trung chất lượng
  max_positions: 1         # Giảm xuống 1 để tập trung hoàn toàn
  max_volume: 0.2          # Giảm xuống 0.2 để an toàn hơn
  min_volume: 0.01         # Giữ nguyên
  risk_per_trade: 0.01     # Giảm xuống 1% để kiểm soát drawdown
  timeframe: 15            # Giữ nguyên 15 phút
  volume_step: 0.01        # Giữ nguyên
  default_volume: 0.03     # Giảm default volume
  # Dynamic position sizing parameters
  dynamic_sizing_enabled: false   # Tắt để đơn giản hóa
  confidence_multiplier: 1.0      # Giữ nguyên
  volatility_adjustment: false    # Tắt để đơn giản hóa
  low_volatility_multiplier: 1.0  # Giữ nguyên
  high_volatility_multiplier: 1.0 # Giữ nguyên

technical_indicators:
  atr_period: 14           # Giảm về 14 để nhạy hơn
  macd_fast: 12            # Tăng lên 12 để ổn định hơn
  macd_slow: 26            # Tăng lên 26 để ổn định hơn
  macd_signal: 9           # Tăng lên 9 để giảm noise
  rsi_period: 21           # Tăng lên 21 để ổn định hơn
  bb_period: 20            # Giữ nguyên
  bb_std: 2.2              # Tăng lên 2.2 để giảm false signals
  ema_fast: 12             # Tăng lên 12 để ổn định hơn
  ema_slow: 26             # Tăng lên 26 để ổn định hơn
  pivot_timeframe: 'D1'    # Giữ nguyên daily pivot

# Market timing optimization for XAUUSD
market_timing:
  enable_session_filter: true      # Bật lọc session
  preferred_sessions:              # Các session tốt nhất cho vàng
    - 'london'                     # London session (8:00-17:00 GMT)
    - 'new_york'                   # New York session (13:00-22:00 GMT)
    - 'overlap'                    # London-NY overlap (13:00-17:00 GMT)
  avoid_sessions:                  # Tránh các session yếu
    - 'asian'                      # Asian session (22:00-8:00 GMT)
    - 'sydney'                     # Sydney session (22:00-7:00 GMT)

  # Specific time filters (GMT)
  enable_hourly_filter: true       # Bật lọc theo giờ
  best_trading_hours:              # Giờ giao dịch tốt nhất
    - [8, 11]                      # London morning
    - [13, 17]                     # London-NY overlap
    - [20, 22]                     # NY evening
  avoid_trading_hours:             # Tránh các giờ yếu
    - [0, 6]                       # Asian night
    - [22, 24]                     # Late night

  # Day of week filters
  enable_dow_filter: true          # Bật lọc theo ngày trong tuần
  best_trading_days: [1, 2, 3, 4] # Thứ 2-5 (tránh thứ 6 và chủ nhật)
  avoid_trading_days: [0, 5, 6]   # Tránh chủ nhật, thứ 6, thứ 7

  # News and events
  enable_news_filter: true         # Bật lọc tin tức
  avoid_high_impact_news: true     # Tránh tin tức tác động cao
  news_buffer_minutes: 30          # Tránh 30 phút trước/sau tin tức
