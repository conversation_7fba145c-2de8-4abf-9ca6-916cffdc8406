ai:
  batch_size: 32
  epsilon_decay: 0.995
  epsilon_end: 0.01
  epsilon_start: 1.0
  learning_rate: 0.001
  memory_size: 10000
  model_type: dqn
  target_update_frequency: 100
  training_frequency: 4
backtest:
  commission: 0.02         # G<PERSON><PERSON><PERSON> từ 0.03 xuống 0.02 để phản ánh thực tế
  end_date: '2024-12-31'   # Cập nhật để test với dữ liệu gần đây hơn
  initial_balance: 229.81  # Giữ nguyên
  spread: 150              # Giảm từ 200 xuống 150 để phù hợp với risk config
  start_date: '2024-01-01' # Cập nhật để test với dữ liệu gần đây hơn
  logging_level: ERROR     # Giữ nguyên để giảm spam log
enable_ai: true
enable_backtesting: false
indicators:
  atr_period: 21           # Tăng từ 14 lên 21 để <PERSON>n định hơn cho vàng
  macd_fast: 8             # G<PERSON><PERSON><PERSON> từ 12 xuống 8 để nh<PERSON><PERSON> hơn với thị trường vàng
  macd_signal: 5           # <PERSON><PERSON><PERSON><PERSON> từ 9 xuống 5 để phản ứng nhanh hơn
  macd_slow: 21            # Giảm từ 26 xuống 21 để cân bằng
  pivot_method: standard   # Giữ nguyên standard method
live_trading: true
logging_level: INFO
mt5:
  login: 126780412
  magic_number: 234000
  password: 'Exnessdz123@199'
  server: 'Exness-MT5Real7'
  symbol: XAUUSD
risk:
  max_drawdown: 0.50  # Tăng lên 50% để cho phép giao dịch linh hoạt hơn
  max_spread: 150     # Giảm từ 200 xuống 150 để tránh giao dịch khi spread cao
  stop_loss_atr_multiplier: 1.8  # Tăng từ 1.5 lên 1.8 để cân bằng
  take_profit_atr_multiplier: 3.5  # Giảm từ 4.0 xuống 3.5 để dễ đạt target hơn
  trailing_stop: true
  trailing_stop_distance: 40  # Tăng từ 30 lên 40 để giảm noise
  margin_level_min: 250       # Giảm từ 300 xuống 250 để cân bằng
  max_total_risk_exposure: 1.2  # Tăng từ 100% lên 120% để linh hoạt hơn
strategy:
  scoring:
    macd_crossover_bullish: 0.5  # Tăng từ 0.4 lên 0.5 - crossover là tín hiệu mạnh
    macd_crossover_bearish: 0.5  # Tăng từ 0.4 lên 0.5 - crossover là tín hiệu mạnh
    macd_trend_bullish: 0.15     # Giảm từ 0.2 xuống 0.15 - trend ít quan trọng hơn crossover
    macd_trend_bearish: 0.15     # Giảm từ 0.2 xuống 0.15 - trend ít quan trọng hơn crossover
    pivot_above: 0.25            # Tăng từ 0.2 lên 0.25 - pivot points quan trọng cho vàng
    pivot_below: 0.25            # Tăng từ 0.2 lên 0.25 - pivot points quan trọng cho vàng
    market_trend_bullish: 0.3    # Tăng từ 0.2 lên 0.3 - trend tổng thể rất quan trọng
    market_trend_bearish: 0.3    # Tăng từ 0.2 lên 0.3 - trend tổng thể rất quan trọng
    ai_prediction_weight: 0.3    # Giảm từ 0.5 xuống 0.3 - ít phụ thuộc AI hơn
    technical_weight: 1.2        # Tăng từ 1.0 lên 1.2 - tăng trọng số technical
    ai_weight: 0.0               # Giữ nguyên 0.0 - tắt AI
    min_signal_strength: 0.75    # Tăng từ 0.6 lên 0.75 - chỉ giao dịch tín hiệu mạnh
    min_ai_confidence: 0.8       # Tăng từ 0.7 lên 0.8 - yêu cầu AI confidence cao hơn
trading:
  max_daily_trades: 8      # Tăng từ 6 lên 8 để cân bằng cơ hội
  max_positions: 2         # Giữ nguyên 2 để tập trung
  max_volume: 0.3          # Giảm từ 0.5 xuống 0.3 để an toàn hơn
  min_volume: 0.01         # Giữ nguyên
  risk_per_trade: 0.012    # Giảm từ 1.5% xuống 1.2% để bảo vệ vốn tốt hơn
  timeframe: 15            # Giữ nguyên 15 phút
  volume_step: 0.01        # Giữ nguyên
